#!/usr/bin/env python3
"""
测试重构后的 advance_python_step 方法。

验证新的统一方法是否正确工作，以及向后兼容的方法是否仍然可用。
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from libraries.workflow_state import WorkflowState
from libraries.workflow_models import WorkflowStep, UserInputStep, ExecuteStep, ActionDefinition
from message import UserMessage


class TestWorkflowGenerator:
    """测试用的简单工作流生成器"""
    
    def __init__(self):
        self.step_count = 0
    
    async def __anext__(self):
        """模拟生成器的 __anext__ 方法"""
        self.step_count += 1
        if self.step_count == 1:
            return UserInputStep(name="test_input", description="测试用户输入步骤")
        elif self.step_count == 2:
            return ExecuteStep(
                name="test_execute", 
                description="测试执行步骤",
                actions=[ActionDefinition(names=["test_tool"], min_calls=1, max_calls=1)]
            )
        else:
            raise StopAsyncIteration("测试工作流结束")
    
    async def asend(self, value):
        """模拟生成器的 asend 方法"""
        print(f"生成器收到值: {value}")
        self.step_count += 1
        if self.step_count == 2:
            return ExecuteStep(
                name="test_execute", 
                description="测试执行步骤",
                actions=[ActionDefinition(names=["test_tool"], min_calls=1, max_calls=1)]
            )
        else:
            raise StopAsyncIteration("测试工作流结束")


async def test_advance_python_step():
    """测试新的 advance_python_step 方法"""
    print("=== 测试新的 advance_python_step 方法 ===")
    
    # 创建工作流状态
    state = WorkflowState()
    state.source_type = 'python'
    state.workflow_name = 'test_workflow'
    state.generator_instance = TestWorkflowGenerator()
    
    try:
        # 测试 1: 不发送值（相当于原来的 advance_python_step_next）
        print("测试 1: 调用 advance_python_step() 不发送值")
        await state.advance_python_step()
        current_step = state.get_current_step()
        print(f"当前步骤: {current_step.name if current_step else 'None'}")
        print(f"是否等待用户输入: {state.is_waiting_for_user}")
        
        # 测试 2: 发送用户消息（相当于原来的 advance_python_step_send）
        print("\n测试 2: 调用 advance_python_step(user_message) 发送用户消息")
        user_msg = UserMessage(content="测试用户输入")
        await state.advance_python_step(user_msg)
        current_step = state.get_current_step()
        print(f"当前步骤: {current_step.name if current_step else 'None'}")
        print(f"是否等待用户输入: {state.is_waiting_for_user}")
        
        print("\n✅ 新方法测试成功！")
        
    except Exception as e:
        print(f"❌ 新方法测试失败: {e}")
        return False
    
    return True


async def test_backward_compatibility():
    """测试向后兼容的方法"""
    print("\n=== 测试向后兼容的方法 ===")
    
    # 创建工作流状态
    state = WorkflowState()
    state.source_type = 'python'
    state.workflow_name = 'test_workflow_compat'
    state.generator_instance = TestWorkflowGenerator()
    
    try:
        # 测试旧的 advance_python_step_next 方法
        print("测试: 调用 advance_python_step_next()")
        await state.advance_python_step_next()
        current_step = state.get_current_step()
        print(f"当前步骤: {current_step.name if current_step else 'None'}")
        
        # 测试旧的 advance_python_step_send 方法
        print("\n测试: 调用 advance_python_step_send(user_message)")
        user_msg = UserMessage(content="测试用户输入")
        await state.advance_python_step_send(user_msg)
        current_step = state.get_current_step()
        print(f"当前步骤: {current_step.name if current_step else 'None'}")
        
        print("\n✅ 向后兼容测试成功！")
        
    except Exception as e:
        print(f"❌ 向后兼容测试失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    print("开始测试 advance_python_step 重构...")
    
    # 测试新方法
    success1 = await test_advance_python_step()
    
    # 测试向后兼容
    success2 = await test_backward_compatibility()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！重构成功！")
        return 0
    else:
        print("\n💥 测试失败！")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
